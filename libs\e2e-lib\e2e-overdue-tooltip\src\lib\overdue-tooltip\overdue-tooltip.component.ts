import { Component, Input } from '@angular/core';
import { E2eOverdueSummary, LoanOverdueSummary } from '@e2e/lib/types';
@Component({
  selector: 'e2e-overdue-tooltip',
  templateUrl: './overdue-tooltip.component.html',
  styleUrls: ['./overdue-tooltip.component.scss'],
})
export class OverdueTooltipComponent {
  @Input() totalOverdue = {} as Partial<LoanOverdueSummary>;
  @Input() tooltipPosition: 'left' | 'right' | 'bottom' | 'top' = 'top';
  @Input() autoWidth = true;

  trackByPortfolioId(index: number, item: E2eOverdueSummary) {
    return item.portfolioId;
  }
}
