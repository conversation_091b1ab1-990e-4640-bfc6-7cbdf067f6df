import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  E2eEarlyRepaymentLoan,
  E2eEarlyRepaymentOverview,
  E2eEarlyRepaymentPortfolio,
  E2eEarlyRepaymentTrack,
  EarlyRepaymentSelectedPortfolio,
  EarlyRepaymentTypes,
} from '@e2e/lib/types';
import { Store } from '@ngrx/store';
import { selectAllowedOnlyDebtRepayment } from '@tenant-management/lib/state';
import { Observable } from 'rxjs';
import { selectSelectedPortfolios } from '../../+state';
import { CreditLimitDetailsPageActions } from '../../+state/actions';

export interface SelectedPortfolio {
  portfolio: E2eEarlyRepaymentPortfolio;
  mode: EarlyRepaymentTypes | null;
}

interface LoanWithTrackId extends E2eEarlyRepaymentLoan {
  externalId: string;
}
@Component({
  selector: 'e2e-early-repayment-money-allocation',
  templateUrl: './early-repayment-money-allocation.component.html',
  styleUrls: ['./early-repayment-money-allocation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EarlyRepaymentMoneyAllocationComponent
  implements OnInit, OnChanges
{
  EarlyRepaymentTypes = EarlyRepaymentTypes;
  selectedPortfolio: E2eEarlyRepaymentPortfolio | null = null;
  @Input() earlyRepaymentOverview = {} as E2eEarlyRepaymentOverview | null;
  // portfolioMode: EarlyRepaymentTypes | null = null; // default
  @Input() portfolioMode?: EarlyRepaymentTypes;
  @Input() totalFeeAmount = 0;
  @Input() amountToRepay?: any;
  @Input() repaymentTypeFieldValue?: EarlyRepaymentTypes;
  @Output() totalSelectedLoanAmountChanged = new EventEmitter<number>();

  selectedTracks: { [trackId: string]: EarlyRepaymentTypes } = {};
  // selectedLoans: number[] = [];

  selectedTrackStates: {
    [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes };
  } = {};
  // selectedTrackStates: Record<string, { selected: boolean; mode: EarlyRepaymentTypes }> = {};
  selectedLoans: number[] = [];

  selectedTrackIds: string[] = [];
  selectedLoanIds: number[] = [];

  selectedPortfolios: SelectedPortfolio[] = [];

  selectedPortfoliosMap: { [id: string]: boolean } = {};

  // selectedPortfolios$: Observable<SelectedPortfolio[]>;
  selectedPortfolios$ = new Observable<SelectedPortfolio[]>();
  selectAllowedOnlyDebtRepayment$ = new Observable<boolean>();

  totalLoanAmount = 0;

  constructor(private store: Store, private cd: ChangeDetectorRef) {}

  get computedTracks(): E2eEarlyRepaymentTrack[] {
    let tracks: E2eEarlyRepaymentTrack[] = [];
    // Loop over each selected portfolio and aggregate its tracks
    for (const sel of this.selectedPortfolios) {
      if (sel.portfolio && sel.portfolio.tracks) {
        tracks = tracks.concat(sel.portfolio.tracks);
      }
    }
    return tracks;
  }
  get computedLoans(): E2eEarlyRepaymentLoan[] {
    let loans: E2eEarlyRepaymentLoan[] = [];
    // Loop through each selected portfolio and add its loans from each track
    for (const sel of this.selectedPortfolios) {
      if (sel.portfolio && sel.portfolio.tracks) {
        for (const track of sel.portfolio.tracks) {
          // Optionally, you can check track mode to decide whether to include the loans
          loans = loans.concat(track.loans);
        }
      }
    }
    return loans;
  }

  // 1) THE TWO NEW MAPS
  loanModes: { [loanId: number]: EarlyRepaymentTypes } = {};
  loanAmountInputs: { [loanId: number]: string } = {};

  ngOnInit() {
    if (this.repaymentTypeFieldValue === EarlyRepaymentTypes.Full) {
      // this.selectAllPortfolios();
      this.resetAllRepaymentTypes();
      this.applyFullModeToAll();
    }

    if (this.repaymentTypeFieldValue === EarlyRepaymentTypes.Partial) {
      this.resetAllRepaymentTypes();
      // Нищо не селектираме по подразбиране — всичко е отключено
    }

    this.selectedPortfolios$ = this.store.select(selectSelectedPortfolios);

    this.selectAllowedOnlyDebtRepayment$ = this.store.select(
      selectAllowedOnlyDebtRepayment
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['repaymentTypeFieldValue']) {
      const value = changes['repaymentTypeFieldValue'].currentValue;

      if (value === EarlyRepaymentTypes.Full) {
        this.resetAllRepaymentTypes();
        this.applyFullModeToAll();
      } else if (value === EarlyRepaymentTypes.Partial) {
        this.resetAllRepaymentTypes();
      }
      this.recalculateTotal();
    }
  }

  onPortfolioSelected(portfolio: E2eEarlyRepaymentPortfolio) {
    this.selectedPortfolio = portfolio;
    if (this.portfolioMode === EarlyRepaymentTypes.Full) {
      this.selectAllTracksAndLoans(portfolio);
    } else {
      this.selectedTrackStates = {};
    }
  }

  onLoanToggle(loanId: number, selected: boolean) {
    if (selected) {
      if (!this.selectedLoans.includes(loanId)) {
        this.selectedLoans.push(loanId);
      }
    } else {
      this.selectedLoans = this.selectedLoans.filter((id) => id !== loanId);
    }
  }

  selectAllTracksAndLoans(portfolio: E2eEarlyRepaymentPortfolio): void {
    this.selectedTrackStates = {};

    portfolio.tracks.forEach((track) => {
      this.selectedTrackStates[track.externalId] = {
        selected: true,
        mode: EarlyRepaymentTypes.Full,
      };
    });
  }

  updateVisibleLoans() {
    this.selectedLoans = [];

    Object.entries(this.selectedTrackStates).forEach(([trackId, state]) => {
      if (!state.selected) return;

      const track = this.selectedPortfolio?.tracks.find(
        (t) => t.externalId === trackId
      );
      if (!track) return;

      if (state.mode === EarlyRepaymentTypes.Full) {
        this.selectedLoans.push(...track.loans.map((loan) => loan.id));
      }
      // partial: loan selection handled manually
    });
  }

  getVisibleLoans(): E2eEarlyRepaymentLoan[] {
    if (!this.selectedPortfolio) return [];

    const result: E2eEarlyRepaymentLoan[] = [];

    for (const [trackId, state] of Object.entries(this.selectedTrackStates)) {
      if (!state.selected) continue;

      const track = this.selectedPortfolio.tracks.find(
        (t) => t.externalId === trackId
      );
      if (!track) continue;

      if (state.mode === EarlyRepaymentTypes.Full) {
        result.push(...track.loans);
      }

      // Partial selection: add only loans selected manually (optional logic if needed)
    }

    return result;
  }

  get visibleLoans(): E2eEarlyRepaymentLoan[] {
    if (!this.selectedPortfolio) return [];

    const result: E2eEarlyRepaymentLoan[] = [];

    for (const [trackId, state] of Object.entries(this.selectedTrackStates)) {
      if (!state.selected) continue;

      const track = this.selectedPortfolio.tracks.find(
        (t) => t.externalId === trackId
      );
      if (!track) continue;

      if (state.mode === EarlyRepaymentTypes.Full) {
        result.push(...track.loans);
      }
      // If 'PARTIAL', show nothing or show manually selected loans if you're tracking them
    }

    return result;
  }
  private recalculateTotal(): void {
    // Предполагаме, че selectedPortfolios и selectedLoanIds са актуални масиви в този обект
    this.totalLoanAmount = this.getTotalSelectedLoanAmount(
      this.selectedPortfolios,
      this.selectedLoanIds
    );
    let amount =
      this.amountToRepay -
      (this.earlyRepaymentOverview
        ? this.earlyRepaymentOverview.totalDebtAmount
        : 0) -
      this.totalLoanAmount;
    if (this.repaymentTypeFieldValue === EarlyRepaymentTypes.Partial) {
      amount -= this.totalFeeAmount;
    }
    this.store.dispatch(
      CreditLimitDetailsPageActions.updateTotalSelectedLoanAmount({ amount })
    );
  }

  // private clearPortfolioState(portfolio: E2eEarlyRepaymentPortfolio) {
  //   // за всеки трек изтриваме избора му
  //   portfolio.tracks?.forEach(track => {
  //     delete this.selectedTrackStates[track.externalId];
  //     this.selectedTrackIds = this.selectedTrackIds.filter(id => id !== track.externalId);
  //     // за всеки loan в този track почистваме и loanSelection + modes + inputs
  //     track.loans.forEach(loan => {
  //       this.selectedLoanIds = this.selectedLoanIds.filter(lid => lid !== loan.id);
  //       delete this.loanModes[loan.id];
  //       delete this.loanAmountInputs[loan.id];
  //     });
  //   });
  //   // ако ползваш totalLoanAmount property, можеш след това да обновиш:
  //   this.totalLoanAmount = this.getTotalSelectedLoanAmount(
  //     /* тук подай текущия масив от selectedPortfolios от state */,
  //     this.selectedLoanIds,
  //     this.selectedTrackStates
  //   );
  // }

  onPortfoliosSelectionChanged(selected: SelectedPortfolio[]): void {
    // Update the local array of selected portfolios.
    this.selectedPortfolios = selected;
  }

  isPortfolioSelected(
    portfolio: E2eEarlyRepaymentPortfolio,
    selectedPortfolios: SelectedPortfolio[]
  ): boolean {
    return selectedPortfolios.some((sel) => sel.portfolio.id === portfolio.id);
  }

  selectAllPortfolios(): void {
    if (!this.earlyRepaymentOverview?.portfolios) return;

    for (const portfolio of this.earlyRepaymentOverview.portfolios) {
      const trackStates: {
        [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes };
      } = {};
      const loanStates: {
        [loanId: number]: {
          selected: boolean;
          mode: EarlyRepaymentTypes;
          amount?: number;
        };
      } = {};

      for (const track of portfolio.tracks || []) {
        trackStates[track.externalId] = {
          selected: true,
          mode: EarlyRepaymentTypes.Full,
        };

        for (const loan of track.loans || []) {
          loanStates[loan.id] = {
            selected: true,
            mode: EarlyRepaymentTypes.Full,
          };
        }
      }

      this.store.dispatch(
        CreditLimitDetailsPageActions.selectPortfolio({
          portfolio,
          mode: EarlyRepaymentTypes.Full,
          trackStates,
          loanStates,
        })
      );
    }
  }

  getLoansForPortfolio(
    portfolio: E2eEarlyRepaymentPortfolio
  ): LoanWithTrackId[] {
    if (!portfolio.tracks) {
      return [];
    }
    return portfolio.tracks.flatMap((track) =>
      (track.loans || []).map((loan) => ({
        ...loan,
        externalId: track.externalId,
      }))
    );
  }

  // // Called when the child signals a portfolio toggle. Dispatch the appropriate actions.
  // onPortfolioToggle(event: {
  //   portfolio: E2eEarlyRepaymentPortfolio;
  //   checked: boolean;
  // }): void {
  //   if (event.checked) {
  //     const portfolioMode = this.getPortfolioMode(
  //       event.portfolio,
  //       this.selectedPortfolios
  //     );
  //     this.store.dispatch(
  //       CreditLimitDetailsPageActions.selectPortfolio({
  //         portfolio: event.portfolio,
  //         mode: portfolioMode,
  //       })
  //     );
  //   } else {
  //     // this.clearPortfolioState(event.portfolio);
  //     this.store.dispatch(
  //       CreditLimitDetailsPageActions.deselectPortfolio({
  //         portfolioId: event.portfolio.id,
  //       })
  //     );

  //     // ⬇️ тук чистиш всички данни свързани с този портфейл:
  //     // 1) махаш стейта на track-овете
  //     for (const track of event.portfolio.tracks || []) {
  //       delete this.selectedTrackStates[track.externalId];
  //     }
  //     // 2) махаш всички loans, които принадлежат на този портфейл
  //     const loanIdsToRemove = new Set(
  //       (event.portfolio.tracks || []).flatMap((t) => t.loans.map((l) => l.id))
  //     );
  //     this.selectedLoanIds = this.selectedLoanIds.filter(
  //       (id) => !loanIdsToRemove.has(id)
  //     );
  //     // ако имаш loanModes и loanAmountInputs, можеш да ги почистиш също:
  //     for (const id of Array.from(loanIdsToRemove)) {
  //       delete this.loanModes[id];
  //       delete this.loanAmountInputs[id];
  //     }
  //   }

  //   // накрая ре‐калкулираш total-а
  //   this.recalculateTotal();
  // }
  onPortfolioToggle(event: {
    portfolio: E2eEarlyRepaymentPortfolio;
    checked: boolean;
  }): void {
    const { portfolio, checked } = event;

    if (checked) {
      // 🔶 Подготовка на trackStates
      const trackStates: {
        [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes };
      } = {};

      for (const track of portfolio.tracks || []) {
        const state = this.selectedTrackStates[track.externalId];
        if (state) {
          trackStates[track.externalId] = {
            selected: state.selected,
            mode: state.mode,
          };
        }
      }

      // 🔶 Подготовка на loanStates
      const loanStates: {
        [loanId: number]: {
          selected: boolean;
          mode: EarlyRepaymentTypes;
          amount?: number;
        };
      } = {};

      for (const track of portfolio.tracks || []) {
        for (const loan of track.loans || []) {
          const isTrackFull =
            trackStates[track.externalId]?.mode === EarlyRepaymentTypes.Full;

          if (isTrackFull) {
            loanStates[loan.id] = {
              selected: true,
              mode: EarlyRepaymentTypes.Full,
            };
          } else if (this.selectedLoanIds.includes(loan.id)) {
            const mode = this.loanModes[loan.id];
            if (!mode) continue;

            loanStates[loan.id] = {
              selected: true,
              mode,
              ...(mode === EarlyRepaymentTypes.Partial && {
                amount: parseFloat(this.loanAmountInputs[loan.id] || '0'),
              }),
            };
          }
        }
      }

      this.store.dispatch(
        CreditLimitDetailsPageActions.selectPortfolio({
          portfolio,
          mode: this.getPortfolioMode(portfolio, this.selectedPortfolios),
          trackStates,
          loanStates,
        })
      );
    } else {
      this.store.dispatch(
        CreditLimitDetailsPageActions.deselectPortfolio({
          portfolioId: portfolio.id,
        })
      );

      // Изчистване на състоянието (може да се изнесе в метод)
      for (const track of portfolio.tracks || []) {
        delete this.selectedTrackStates[track.externalId];
      }

      const loanIdsToRemove = new Set(
        (portfolio.tracks || []).flatMap((t) => t.loans.map((l) => l.id))
      );
      this.selectedLoanIds = this.selectedLoanIds.filter(
        (id) => !loanIdsToRemove.has(id)
      );
      for (const id of Array.from(loanIdsToRemove)) {
        delete this.loanModes[id];
        delete this.loanAmountInputs[id];
      }
    }

    // this.recalculateTrigger$.next(); // тригър за преизчисление
    this.recalculateTotal();
  }

  // onPortfolioModeChange(event: {
  //   portfolio: E2eEarlyRepaymentPortfolio;
  //   mode: EarlyRepaymentTypes;
  // }): void {
  //   const { portfolio, mode } = event;

  //   // Update store: portfolio mode
  //   this.store.dispatch(
  //     CreditLimitDetailsPageActions.updatePortfolioMode({
  //       portfolioId: portfolio.id,
  //       mode,
  //     })
  //   );

  //   this.store.dispatch(
  //     CreditLimitDetailsPageActions.changePortfolioMode({
  //       portfolioId: event.portfolio.id,
  //       mode: event.mode,
  //     })
  //   );

  //   if (mode === EarlyRepaymentTypes.Partial) {
  //     // Remove that portfolio's tracks
  //     portfolio.tracks?.forEach((track) => {
  //       delete this.selectedTrackStates[track.externalId];
  //     });

  //     // Remove that portfolio's loans
  //     const loanIdsToRemove = new Set(
  //       portfolio.tracks?.flatMap((t) => t.loans.map((l) => l.id)) || []
  //     );
  //     this.selectedLoanIds = this.selectedLoanIds.filter(
  //       (id) => !loanIdsToRemove.has(id)
  //     );
  //   }
  // }

  onPortfolioModeChange(event: {
    portfolio: E2eEarlyRepaymentPortfolio;
    mode: EarlyRepaymentTypes;
  }): void {
    const { portfolio, mode } = event;

    const trackStates: {
      [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes };
    } = {};

    const loanStates: {
      [loanId: number]: {
        selected: boolean;
        mode: EarlyRepaymentTypes;
        amount?: number;
      };
    } = {};

    for (const track of portfolio.tracks || []) {
      const isFull = mode === EarlyRepaymentTypes.Full;

      trackStates[track.externalId] = {
        selected: isFull,
        mode: isFull ? EarlyRepaymentTypes.Full : EarlyRepaymentTypes.Partial,
      };

      for (const loan of track.loans || []) {
        if (isFull) {
          this.loanModes[loan.id] = EarlyRepaymentTypes.Full;
        } else {
          delete this.loanModes[loan.id];
          delete this.loanAmountInputs[loan.id];
        }

        loanStates[loan.id] = {
          selected: isFull,
          mode: isFull ? EarlyRepaymentTypes.Full : EarlyRepaymentTypes.Partial,
          ...(isFull
            ? {}
            : {
                amount: parseFloat(this.loanAmountInputs[loan.id] || '0'),
              }),
        };
      }
    }

    // Dispatch enriched data to store
    this.store.dispatch(
      CreditLimitDetailsPageActions.selectPortfolio({
        portfolio: {
          ...portfolio,
          earlyRepaymentType: mode,
          allocatedDebtRepayment: 0,
          tracks: (portfolio.tracks || []).map((track) => ({
            ...track,
            earlyRepaymentType:
              mode === EarlyRepaymentTypes.Full
                ? EarlyRepaymentTypes.Full
                : null,
            payers: [],
            loans: (track.loans || []).map((loan) => ({
              ...loan,
              earlyRepaymentType:
                mode === EarlyRepaymentTypes.Full
                  ? EarlyRepaymentTypes.Full
                  : null,
              earlyRepaymentAmount:
                mode === EarlyRepaymentTypes.Partial
                  ? loan.earlyRepaymentAmount ?? 0
                  : 0,
            })),
          })),
        },
        mode,
        trackStates,
        loanStates,
      })
    );

    // Clean local states if Partial
    if (mode === EarlyRepaymentTypes.Partial) {
      for (const track of portfolio.tracks || []) {
        delete this.selectedTrackStates[track.externalId];
      }

      const loanIdsToRemove = new Set(
        portfolio.tracks?.flatMap((t) => t.loans.map((l) => l.id)) || []
      );
      this.selectedLoanIds = this.selectedLoanIds.filter(
        (id) => !loanIdsToRemove.has(id)
      );
    }
  }

  onTrackSelectionChanged(event: {
    track: E2eEarlyRepaymentTrack;
    selected: boolean;
    mode: EarlyRepaymentTypes;
  }) {
    const trackId = event.track.externalId;
    const loanIds = (event.track.loans || []).map((l) => l.id);

    // update the map for the loans component
    // this.selectedTrackStates[trackId] = {
    //   selected: event.selected,
    //   mode: event.mode,
    // };
    this.selectedTrackStates = {
      ...this.selectedTrackStates,
      [trackId]: {
        selected: event.selected,
        mode: event.mode,
      },
    };

    // Update store
    const portfolioId = this.getPortfolioIdForTrack(event.track); // или подай го като @Input
    if (!portfolioId) return;
    this.store.dispatch(
      CreditLimitDetailsPageActions.updateTrackMode({
        portfolioId,
        trackId,
        mode: event.mode,
      })
    );

    if (event.mode === EarlyRepaymentTypes.Full) {
      if (!this.selectedTrackIds.includes(trackId)) {
        this.selectedTrackIds.push(trackId);
      }
      this.selectedLoanIds = Array.from(
        new Set([...this.selectedLoanIds, ...loanIds])
      );

      // Задаваме FULL mode на всички loans от този track
      const portfolioId = this.getPortfolioIdForTrack(event.track);
      if (portfolioId) {
        for (const loan of event.track.loans) {
          this.loanModes[loan.id] = EarlyRepaymentTypes.Full;
          this.store.dispatch(
            CreditLimitDetailsPageActions.updateLoanModeAndAmount({
              portfolioId,
              trackId,
              loanId: loan.id,
              mode: EarlyRepaymentTypes.Full,
            })
          );
        }
      }
    } else {
      // Deselect track & its loans in PARTIAL mode
      this.selectedTrackIds = this.selectedTrackIds.filter(
        (id) => id !== trackId
      );
      this.selectedLoanIds = this.selectedLoanIds.filter(
        (id) => !loanIds.includes(id)
      );

      // Изчистваме loanModes
      for (const loan of event.track.loans) {
        delete this.loanModes[loan.id];
        delete this.loanAmountInputs[loan.id];
      }
    }
    this.cd.detectChanges();
  }

  onLoanSelectionChanged(event: {
    loan: E2eEarlyRepaymentLoan;
    selected: boolean;
    mode: EarlyRepaymentTypes;
  }) {
    const loanId = event.loan.id;

    if (event.selected) {
      // Immutable add
      if (!this.selectedLoanIds.includes(loanId)) {
        this.selectedLoanIds = [...this.selectedLoanIds, loanId];
      }
    } else {
      // Immutable remove
      this.selectedLoanIds = this.selectedLoanIds.filter((id) => id !== loanId);
    }
  }
  get hasTracks(): boolean {
    return (
      this.earlyRepaymentOverview?.portfolios?.some(
        (portfolio) => portfolio.tracks && portfolio.tracks.length > 0
      ) || false
    );
  }

  getTotalSelectedLoanAmount(
    selectedPortfolios: EarlyRepaymentSelectedPortfolio[],
    selectedLoanIds: number[]
    // selectedTrackStates: { [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes } }
  ): number {
    let total = 0;

    for (const sp of selectedPortfolios) {
      for (const track of sp.portfolio.tracks ?? []) {
        const trackState = this.selectedTrackStates[track.externalId];

        // 1) Portfolio Full → всички track → всички loans
        if (sp.mode === EarlyRepaymentTypes.Full) {
          for (const loan of track.loans ?? []) {
            total += loan.amountFullRepayment ?? 0;
          }
          continue;
        }

        // 2) Portfolio Partial, но track Full → всички loans на този track
        if (
          trackState?.selected &&
          trackState.mode === EarlyRepaymentTypes.Full
        ) {
          for (const loan of track.loans ?? []) {
            total += loan.amountFullRepayment ?? 0;
          }
          continue;
        }

        // 3) Portfolio Partial и track Partial → броим само избраните loans
        if (
          trackState?.selected &&
          trackState.mode === EarlyRepaymentTypes.Partial
        ) {
          for (const loan of track.loans ?? []) {
            if (!selectedLoanIds.includes(loan.id)) {
              continue; // не е маркиран — пропускаме
            }
            // ако loan-level е Full → добави цялата сума
            const lm = this.loanModes[loan.id];
            if (lm === EarlyRepaymentTypes.Full) {
              total += loan.amountFullRepayment ?? 0;
            } else if (lm === EarlyRepaymentTypes.Partial) {
              // Partial на loan → добави въведения amount
              total += parseFloat(this.loanAmountInputs[loan.id] || '0');
            } else {
              // Не е избран loanMode => не включваме сумата
              continue;
            }
          }
        }
        // иначе track не е селектиран → нищо не правим
      }
    }

    let amount =
      this.amountToRepay -
      (this.earlyRepaymentOverview?.totalDebtAmount || 0) -
      total;
    if (this.repaymentTypeFieldValue === EarlyRepaymentTypes.Partial) {
      amount -= this.totalFeeAmount;
    }
    const roundedAmount = Math.round(amount * 100) / 100;
    this.store.dispatch(
      CreditLimitDetailsPageActions.updateTotalSelectedLoanAmount({
        amount: roundedAmount,
      })
    );

    return total;
  }

  // This method expects that selectedPortfolios comes from your NgRx store and includes the mode (either EarlyRepaymentTypes.Full or 'PARTIAL').
  getPortfolioMode(
    portfolio: E2eEarlyRepaymentPortfolio,
    selectedPortfolios: SelectedPortfolio[]
  ): EarlyRepaymentTypes | undefined {
    const found = selectedPortfolios.find(
      (sp) => sp.portfolio.id === portfolio.id
    );
    // return found?.mode ?? EarlyRepaymentTypes.Partial;
    return found?.mode ?? undefined;
  }

  onLoanToggled(event: { loanId: number; selected: boolean }) {
    const { loanId, selected } = event;
    if (selected) {
      this.selectedLoanIds = [...this.selectedLoanIds, loanId];
    } else {
      this.selectedLoanIds = this.selectedLoanIds.filter((id) => id !== loanId);
    }
    // this.recalculateTotal();
  }

  onLoanModeChanged(event: {
    loan: E2eEarlyRepaymentLoan;
    mode: EarlyRepaymentTypes;
  }) {
    const loan = event.loan;
    this.loanModes[loan.id] = event.mode;

    //const loan = this.resolveLoanById(event.loanId); // метод който намира loan от overview
    if (!loan) return;
    const trackId = loan.externalId;
    const portfolioId = this.getPortfolioIdForLoan(loan); // подай чрез @Input ако е нужно
    if (!portfolioId) return;
    this.store.dispatch(
      CreditLimitDetailsPageActions.updateLoanModeAndAmount({
        portfolioId,
        trackId,
        loanId: loan.id,
        mode: event.mode,
      })
    );

    this.recalculateTotal();
  }
  onLoanAmountInput(
    event: {
      loanId: number;
      amount: string;
      invalidAmount: boolean;
    },
    track: E2eEarlyRepaymentTrack
  ) {
    // this.loanAmountInputs[event.loanId] = event.amount;
    const parsedAmount = parseFloat(event.amount.replace(/,/g, '')) || 0;
    this.loanAmountInputs[event.loanId] = event.amount;

    const loan = this.resolveLoanById(event.loanId);
    if (!loan) return;
    const trackId = track.externalId;
    if (!loan) return;
    const portfolioId = this.getPortfolioIdForLoan(loan);
    if (!portfolioId) return;
    let isFormValid: boolean | null;
    if (event.invalidAmount) {
      this.store.dispatch(
        CreditLimitDetailsPageActions.setIsFormValid({ isFormValid: false })
      );
    } else {
      this.store.dispatch(
        CreditLimitDetailsPageActions.setIsFormValid({ isFormValid: true })
      );
    }

    this.store.dispatch(
      CreditLimitDetailsPageActions.updateLoanModeAndAmount({
        portfolioId,
        trackId,
        loanId: loan.id,
        mode: EarlyRepaymentTypes.Partial,
        amount: parsedAmount,
      })
    );
  }

  trackByPortfolio(_idx: number, portfolio: E2eEarlyRepaymentPortfolio) {
    return portfolio.id;
  }

  // trackByPortfolio(_: number, sp: EarlyRepaymentSelectedPortfolio) {
  //   return sp.portfolio.id;
  // }

  trackByTrack(_: number, track: E2eEarlyRepaymentTrack) {
    return track.externalId;
  }

  // trackByLoan(_: number, loan: E2eEarlyRepaymentLoan) {
  //   return loan.id;
  // }

  // get allocationRows(): {
  //   portfolio: E2eEarlyRepaymentPortfolio;
  //   track: E2eEarlyRepaymentTrack;
  //   loans: E2eEarlyRepaymentLoan[];
  // }[] {
  //   if (!this.earlyRepaymentOverview) return [];

  //   const rows: any[] = [];
  //   for (const p of this.earlyRepaymentOverview.portfolios) {
  //     const tracks = p.tracks || [];
  //     if (tracks.length === 0) {
  //       // no tracks → still need one blank “track” row if you like
  //       rows.push({ portfolio: p, track: null, loans: [] });
  //     } else {
  //       // each track yields one row
  //       for (let i = 0; i < tracks.length; i++) {
  //         rows.push({
  //           // show the portfolio cell only on the *first* track row
  //           portfolio: p,
  //           track: tracks[i],
  //           loans: tracks[i].loans || [],
  //           showPortfolio: i === 0,
  //         });
  //       }
  //     }
  //   }
  //   return rows;
  // }

  // trackByRow(
  //   _idx: number,
  //   row: {
  //     portfolio: E2eEarlyRepaymentPortfolio;
  //     track: E2eEarlyRepaymentTrack;
  //   }
  // ) {
  //   // unique per portfolio‐track combo:
  //   return `${row.portfolio.id}-${row.track?.externalId}`;
  // }

  enrichLoansWithTrackId(
    loans: E2eEarlyRepaymentLoan[],
    trackId: string
  ): E2eEarlyRepaymentLoan[] {
    return (loans || []).map((loan) => ({
      ...loan,
      externalId: trackId,
    }));
  }

  resolveLoanById(loanId: number): E2eEarlyRepaymentLoan | undefined {
    return this.earlyRepaymentOverview?.portfolios
      ?.flatMap((p) => p.tracks?.flatMap((t) => t.loans) || [])
      ?.find((l) => l.id === loanId);
  }

  getPortfolioIdForTrack(track: E2eEarlyRepaymentTrack): string | undefined {
    const portfolio = this.earlyRepaymentOverview?.portfolios?.find((p) =>
      p.tracks?.some((t) => t.externalId === track.externalId)
    );
    return portfolio?.id;
  }

  getPortfolioIdForLoan(loan: E2eEarlyRepaymentLoan): string | undefined {
    const portfolio = this.earlyRepaymentOverview?.portfolios?.find((p) =>
      p.tracks?.some((t) => t.loans?.some((l) => l.id === loan.id))
    );
    return portfolio?.id;
  }

  private applyFullModeToAll(): void {
    if (!this.earlyRepaymentOverview?.portfolios) return;

    for (const portfolio of this.earlyRepaymentOverview.portfolios) {
      const fullPortfolio: E2eEarlyRepaymentPortfolio = {
        ...portfolio,
        earlyRepaymentType: EarlyRepaymentTypes.Full,
        allocatedDebtRepayment: 0,
        tracks: (portfolio.tracks || []).map((track) => ({
          ...track,
          earlyRepaymentType: EarlyRepaymentTypes.Full,
          payers: [],
          loans: (track.loans || []).map((loan) => ({
            ...loan,
            earlyRepaymentType: EarlyRepaymentTypes.Full,
            earlyRepaymentAmount: 0,
          })),
        })),
      };

      // Подготвяме и стейтовете за dispatch
      const trackStates = Object.fromEntries(
        (portfolio.tracks || []).map((track) => [
          track.externalId,
          { selected: true, mode: EarlyRepaymentTypes.Full },
        ])
      );

      const loanStates = Object.fromEntries(
        (portfolio.tracks || []).flatMap((track) =>
          (track.loans || []).map((loan) => [
            loan.id,
            { selected: true, mode: EarlyRepaymentTypes.Full },
          ])
        )
      );

      this.store.dispatch(
        CreditLimitDetailsPageActions.selectPortfolio({
          portfolio: fullPortfolio,
          mode: EarlyRepaymentTypes.Full,
          trackStates,
          loanStates,
        })
      );
    }
  }

  private resetAllRepaymentTypes(): void {
    if (!this.earlyRepaymentOverview?.portfolios) return;

    for (const portfolio of this.earlyRepaymentOverview.portfolios) {
      const clearedPortfolio: E2eEarlyRepaymentPortfolio = {
        ...portfolio,
        earlyRepaymentType: null,
        allocatedDebtRepayment: 0,
        tracks: (portfolio.tracks || []).map((track) => ({
          ...track,
          earlyRepaymentType: null,
          payers: [],
          loans: (track.loans || []).map((loan) => ({
            ...loan,
            earlyRepaymentType: null,
            earlyRepaymentAmount: 0,
          })),
        })),
      };

      this.store.dispatch(
        CreditLimitDetailsPageActions.selectPortfolio({
          portfolio: clearedPortfolio,
          mode: null,
          trackStates: {},
          loanStates: {},
        })
      );
    }
  }
}
