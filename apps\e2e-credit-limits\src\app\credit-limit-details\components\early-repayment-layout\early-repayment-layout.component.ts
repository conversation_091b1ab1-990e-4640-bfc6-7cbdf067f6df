import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ConfirmationDialogComponent } from '@e2e/lib/confirmation-dialog';
import {
  CreditLimitEarlyRepayment,
  E2eConfirmationDialog,
  E2eCustomActionsMenuTypes,
  EarlyRepaymentTypes,
} from '@e2e/lib/types';
import { Store } from '@ngrx/store';
import {
  selectAllowedOnlyDebtRepayment,
  selectDateFormat,
} from '@tenant-management/lib/state';
import { SnackBarActions } from '@tenant-management/lib/types';
import { combineLatest, map, Observable, shareReplay, tap } from 'rxjs';
import {
  selectAmountToRepay,
  selectCreditLimitDetails,
  selectEarlyRepaymentForm,
  selectIsFormValid,
  selectOverview,
  selectTotalSelectedLoanAmount,
} from '../../+state';
import { CreditLimitDetailsPageActions } from '../../+state/actions';
import { CreditLimitDetails } from '../../models/credit-limit-details';
import { buttonEnabled } from '../../utils';

@Component({
  selector: 'e2e-early-repayment-layout',
  templateUrl: './early-repayment-layout.component.html',
  styleUrls: ['./early-repayment-layout.component.scss'],
})
export class EarlyRepaymentLayoutComponent implements OnInit {
  getCreditLimitReference$ = new Observable<CreditLimitDetails>();
  isEarlyRepayment = false;
  backUrl = 'operations/credit-limits';
  creditLimitId = '';
  totalSelectedLoanAmount$ = new Observable<any>();
  selectAllowedOnlyDebtRepayment$ = new Observable<any>();
  overview$ = new Observable<any>();
  amountToRepay$ = new Observable<any>();
  selectIsFormValid$ = new Observable<any>();
  getEarlyRepaymentForm$ = new Observable<CreditLimitEarlyRepayment>();
  EarlyRepaymentTypes = EarlyRepaymentTypes;
  //isSimulateDisabled$: Observable<any> = this.store.select(selectEarlyRepaymentForm);
  isSimulateDisabled$: Observable<boolean> = combineLatest([
    this.store.select(selectIsFormValid), // Observable<boolean | undefined>
    this.store.select(selectEarlyRepaymentForm), // Observable<{ repaymentType: string }>
    this.store.select(selectAmountToRepay), // Observable<number | undefined>
    this.store.select(selectOverview), // Observable<{ totalDebtAmount: number }>
    this.store.select(selectAllowedOnlyDebtRepayment), // Observable<boolean>
    this.store.select(selectTotalSelectedLoanAmount), // Observable<boolean>
  ]).pipe(
    map(
      ([
        isFormValid,
        form,
        amountToRepay,
        overview,
        onlyDebtAllowed,
        remainingAmount,
      ]) => {
        const repaymentType = form?.repaymentType;
        let buttonDisabled = true;
        if (overview) {
          if (
            buttonEnabled(
              onlyDebtAllowed,
              repaymentType as EarlyRepaymentTypes,
              overview.totalDebtAmount,
              isFormValid,
              remainingAmount
            )
          ) {
            buttonDisabled = false;
          }
        }
        return buttonDisabled;
      }
    )
  );
  getDateFormat$ = new Observable<string>();

  constructor(
    private store: Store,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getCreditLimitReference$ = this.store
      .select(selectCreditLimitDetails)
      .pipe(
        tap((updatedCreditLimitDetails) => {
          if (updatedCreditLimitDetails) {
            this.creditLimitId = updatedCreditLimitDetails.id;
            this.backUrl = `/operations/credit-limits/${this.creditLimitId}/details`;
          }
        })
      );

    this.totalSelectedLoanAmount$ = this.store.select(
      selectTotalSelectedLoanAmount
    );
    this.overview$ = this.store.select(selectOverview);
    this.amountToRepay$ = this.store.select(selectAmountToRepay);

    this.selectAllowedOnlyDebtRepayment$ = this.store.select(
      selectAllowedOnlyDebtRepayment
    );

    this.getEarlyRepaymentForm$ = this.store.select(selectEarlyRepaymentForm);
    this.selectIsFormValid$ = this.store.select(selectIsFormValid);

    this.router.events.subscribe(() => {
      this.router.url.includes('/simulate')
        ? (this.backUrl =
            'operations/credit-limits/' +
            this.creditLimitId +
            '/early-repayment/details')
        : false;

      this.router.url.includes('/details')
        ? (this.backUrl = 'operations/credit-limits')
        : false;

      this.isEarlyRepayment =
        // this.router.url.includes('/early-repayment') ||
        this.router.url.includes('/simulate');
    });
    this.store.dispatch(CreditLimitDetailsPageActions.clearSimulationRequest());

    this.getDateFormat$ = this.store.select(selectDateFormat).pipe(
      shareReplay({
        bufferSize: 1,
        refCount: true,
      })
    );
  }

  navigateToNextStep() {
    if (this.router.url.includes('/simulate')) {
      this.store.dispatch(
        CreditLimitDetailsPageActions.triggerSimulationRequest()
      );
    } else {
      // We're on the details route → navigate to simulate
      this.router.navigate([
        'operations',
        'credit-limits',
        this.creditLimitId,
        'early-repayment',
        'simulate',
      ]);
    }
  }

  cancelCreditLimit(
    actionType: E2eCustomActionsMenuTypes,
    action: string = SnackBarActions.CancelLimit
  ) {
    if (actionType === E2eCustomActionsMenuTypes.CancelLimitApplication) {
      const dialogData: E2eConfirmationDialog = {
        title: `e2e.confirmationDialogMessages.${actionType}Title`,
        message: `e2e.confirmationDialogMessages.${actionType}Message`,
        confirmationBtnText: 'general.buttons.cancelLimit',
        cancelBtnTex: 'general.buttons.discard',
        action,
      };

      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: dialogData,
      });

      dialogRef.afterClosed().subscribe((action) => {
        if (action) {
          // this.store.dispatch(
          //   CreditLimitDetailsPageActions.cancelCreditLimit()
          // );
        }
      });
    }
  }
}
