<div class="p-2 bg-maWhite rounded-lg shadow-primary border my-2">
  <div class="text-compass-87 font-semibold ms-4">
    <div class="text-xl">
      {{ selectedCount }}
      {{ 'credit-limit-details.moneyAllocation.selected' | transloco }}
    </div>
    <div class="text-xs uppercase">
      <span class="text-compass-60 font-normal">{{
        'credit-limit-details.moneyAllocation.outOf' | transloco
      }}</span>
      {{ loans.length }}
      {{ 'credit-limit-details.moneyAllocation.loans' | transloco }}
    </div>
  </div>

  <div
    *ngFor="let loan of loans; let i = index; trackBy: trackByLoan"
    class="border p-4 rounded-lg my-2 bg-white shadow-primary"
    [ngClass]="{
      'border-orientals': selectedLoanIds.includes(loan.id)
    }"
  >
    <div class="flex items-center justify-between">
      <mat-checkbox
        (change)="onToggleLoan(loan, $event.checked)"
        [checked]="
          portfolioMode === EarlyRepaymentTypes.Full ||
          selectedLoanIds.includes(loan.id)
        "
        [disabled]="
          portfolioMode === EarlyRepaymentTypes.Full ||
          !selectedTrackStates[loan.externalId]?.selected ||
          selectedTrackStates[loan.externalId]?.mode === null ||
          selectedTrackStates[loan.externalId]?.mode === undefined ||
          selectedTrackStates[loan.externalId]?.mode ===
            EarlyRepaymentTypes.Full
        "
      >
        <!-- !selectedTrackStates[loan.externalId]?.mode || -->
        <!-- [disabled]="isDisabled" -->
        <!-- [disabled]="
          portfolioMode === EarlyRepaymentTypes.Full ||
          selectedTrackStates[loan.externalId]?.mode ===
            EarlyRepaymentTypes.Full
        " -->
        <div class="font-bold">{{ loan.referenceName }}</div>
        <span class="text-xs text-compass-60 uppercase">
          {{ 'credit-limit-details.moneyAllocation.fullRepayment' | transloco }}
        </span>
        <span class="text-xs text-compass font-semibold">
          {{ loan.amountFullRepayment | currency }}</span
        >
      </mat-checkbox>

      <div>
        <tm-tooltip
          [escape]="false"
          [tooltipTemplateMessage]="portfolioTooltipTemplate"
          tooltipPosition="left"
        >
          <div #portfolioTooltipTemplate>
            <div class="grid grid-cols-2 gap-2">
              <div class="text-sm font-light">
                {{ 'credit-limit-details.loanTooltip.loanAmount' | transloco }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.loanAmount | currency: currency }}
              </div>
              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.outstandingPrincipal'
                    | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.outstandingPrincipal | currency: currency }}
              </div>
              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.amountFullRepayment'
                    | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.amountFullRepayment | currency: currency }}
              </div>
              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.disbursementDate'
                    | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.disbursementDate | date: 'dd.MM.yyyy' }}
              </div>

              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.maturityDate' | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.maturityDate | date: 'dd.MM.yyyy' }}
              </div>

              <div class="text-sm font-light">
                {{ 'credit-limit-details.loanTooltip.status' | transloco }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.status }}
              </div>

              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.numberOfPayments'
                    | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.numberOfPayments }}
              </div>

              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.interestType' | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.interestType }}
              </div>

              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.regularInterestRate'
                    | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.regularInterestRate }}
              </div>

              <div class="text-sm font-light">
                {{
                  'credit-limit-details.loanTooltip.financialSource' | transloco
                }}
              </div>
              <div class="text-sm font-extrabold">
                {{ loan.financialSource }}
              </div>
            </div>
          </div>
        </tm-tooltip>
      </div>
    </div>

    <div
      *ngIf="
        selectedLoanIds.includes(loan.id) &&
        selectedTrackStates[loan.externalId]?.mode ===
          EarlyRepaymentTypes.Partial
      "
      class="mt-2"
    >
      <!-- [value]="loanModes[loan.id]" -->
      <mat-radio-group
        class="mat-radio-group-horizontal"
        #loanMode="matRadioGroup"
        exportAs="loanMode"
        (change)="onLoanModeChange(loan, $event.value)"
      >
        <mat-radio-button [value]="EarlyRepaymentTypes.Full" class="flex-1">
          {{ 'credit-limit-details.earlyRepayment.full' | transloco }}
        </mat-radio-button>
        <mat-radio-button [value]="EarlyRepaymentTypes.Partial" class="flex-1">
          {{ 'credit-limit-details.earlyRepayment.partial' | transloco }}
        </mat-radio-button>
      </mat-radio-group>

      <div
        *ngIf="
          selectedLoanIds.includes(loan.id) &&
          loanMode.value === EarlyRepaymentTypes.Partial
        "
        class="mt-4"
      >
        <mat-form-field
          *ngIf="getCurrency$ | async as currency"
          appearance="outline"
          class="w-full"
        >
          <mat-label>
            {{ 'credit-limit-details.loanTooltip.financialSource' | transloco }}
          </mat-label>
          <input
            class="text-base"
            matInput
            (input)="onAmountInput(loan, $event, getControl(i))"
            mask="separator.2"
            thousandSeparator=","
            [disabled]="isDisabled"
            [formControl]="getControl(i)"
            [prefix]="
              currency[0].code ? onGetCurrencySymbol(currency[0].code) : ''
            "
          />
          <mat-error *ngIf="getControl(i).errors" class="mb-4">
            {{
              'credit-limit-details.moneyAllocation.amountErrorMessage'
                | transloco
            }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
</div>
