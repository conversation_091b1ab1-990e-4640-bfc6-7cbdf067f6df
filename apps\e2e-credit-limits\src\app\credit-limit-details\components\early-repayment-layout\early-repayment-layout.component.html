<ng-container *ngIf="getCreditLimitReference$ | async as creditLimitReference">
  <div class="flex justify-between items-center mb-5 pe-6">
    <div class="flex items-center">
      <div class="me-3.5">
        <!-- <tm-back-to-previous-page
          [route]="
            'operations/credit-limits/' +
            creditLimitReference.id +
            '/early-repayment/details'
          "
        ></tm-back-to-previous-page> -->
        <ng-container>
          <tm-back-to-previous-page
            [route]="backUrl"
          ></tm-back-to-previous-page>
        </ng-container>
      </div>
      <div class="text-3xl font-bold me-2.5 flex items-center">
        <span class="me-2 title-gradient-primary">
          {{ 'credit-limit-details.earlyRepayment.layoutTitle' | transloco }}
        </span>
        <span class="text-compass me-2 text-[20px] align-text-bottom"
          >&bull;</span
        >
        <mat-icon svgIcon="icon-verified" class="me-1"></mat-icon>
        <span class="text-[22px] text-compass align-text-bottom">
          {{ creditLimitReference.referenceName }}
        </span>
        <tm-tooltip [escape]="false" [tooltipTemplateMessage]="titleTemplate">
          <div #titleTemplate>
            <div class="w-[267px] text-white">
              <h4 class="font-bold text-base">
                {{ creditLimitReference.referenceName }}
              </h4>
              <hr class="bg-white my-2" />
              <dl class="grid grid-cols-2 gap-x-1 gap-y-2">
                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.limitAmount'
                      | transloco
                  }}
                </dt>
                <dd class="text-sm font-semibold text-end">
                  {{
                    creditLimitReference.amount.value
                      | currency: creditLimitReference.amount.currency
                  }}
                </dd>

                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.utilizedAmount'
                      | transloco
                  }}
                </dt>
                <dd class="text-sm font-semibold text-end">
                  {{
                    creditLimitReference.portfoliosCreatedAmount.value
                      | currency
                        : creditLimitReference.portfoliosCreatedAmount.currency
                  }}
                </dd>

                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.unutilizedAmount'
                      | transloco
                  }}
                </dt>
                <dd class="text-sm font-semibold text-end">
                  {{
                    creditLimitReference.availableAmount.value
                      | currency: creditLimitReference.availableAmount.currency
                  }}
                </dd>

                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.effectiveDate'
                      | transloco
                  }}
                </dt>
                <dd
                  class="text-sm font-semibold text-end"
                  *ngIf="getDateFormat$ | async as dateFormat"
                >
                  {{ creditLimitReference.effectiveDate | date: dateFormat }}
                </dd>

                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.maturityDate'
                      | transloco
                  }}
                </dt>
                <dd
                  class="text-sm font-semibold text-end"
                  *ngIf="getDateFormat$ | async as dateFormat"
                >
                  {{ creditLimitReference.endDate | date: dateFormat }}
                </dd>

                <dt class="text-sm">
                  {{
                    'credit-limit-details.investmentLimit.status' | transloco
                  }}
                </dt>
                <dd class="text-sm font-semibold text-end">
                  {{ creditLimitReference.status }}
                </dd>
              </dl>
            </div>
          </div>
        </tm-tooltip>
      </div>
    </div>

    <div class="flex">
      <div>
        <button
          type="button"
          mat-flat-button
          color="primary"
          class="rounded-lg h-11 ms-6"
          (click)="navigateToNextStep()"
          [disabled]="isSimulateDisabled$ | async"
        >
          <!-- [disabled]=" 
           (amountToRepay$ | async) === undefined || (overview$ |
          async) === undefined || (amountToRepay$ | async) <= 0 ||
          ((amountToRepay$ | async) > (overview$ | async)?.totalDebtAmount &&
          (selectAllowedOnlyDebtRepayment$ | async) === true) ||
          (getEarlyRepaymentForm$ | async) === undefined ||
          (getEarlyRepaymentForm$ | async) === false " -->
          <div class="flex items-center">
            <span *ngIf="!isEarlyRepayment" class="text-sm me-2">
              {{ 'general.buttons.simulate' | transloco }}
            </span>
            <span *ngIf="isEarlyRepayment" class="text-sm me-2">
              {{ 'general.buttons.submit' | transloco }}
            </span>
            <mat-icon class="mat-tm-icon mat-tm-icon-size-20">
              arrow_forward
            </mat-icon>
          </div>
        </button>
      </div>
    </div>
  </div>

  <div>
    <div class="grow pe-4">
      <div class="operation-details-height ps-1 pe-2 pb-4">
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</ng-container>
