<!-- <div class="main-card" *ngIf="getSelectedPortfolios$ | async as selectedPortfolios"> -->
<ng-container *ngIf="getCurrency$ | async as currency">
  <ng-container *ngIf="getEarlyRepaymentForm$ | async as earlyRepaymentForm">
    <div *ngIf="getSelectedPortfolios$ | async as selectedPortfolios">
      <!-- TODO Hide simulation section and chart for now -->
      <!-- <div class="mb-6 main-card">
    <div class="grid grid-cols-7 gap-4">
      <div class="grid col-span-2 mx-4">
        <div class="text-compass-87 font-semibold text-2xl">
          Request simulation
        </div>

        <div class="flex items-center">
          <mat-icon
            svgIcon="icon-calendar"
            class="icon-color icon-color-compass mat-tm-icon mat-tm-icon-size-21 me-4"
          ></mat-icon>

          <div>
            <div class="text-compass-60 text-sm">Effective date</div>
            <div class="text-compass font-semibold text-sm">07.02.2025</div>
          </div>
        </div>
      </div>

      <div class="grid col-span-3 mx-4">
        <div class="flex items-center">
          <div class="h-32 w-32 relative me-4">
            <canvas
              [e2eDoughnutChart]="setSelectedPortfoliosChartDataset(selectedPortfolios)"
            ></canvas>

            <div class="absolute w-full text-center top-1/4">
              <div class="text-xs mx-auto text-compass-60 w-1/2 font-medium">
                Total repayment
              </div>
              <div class="text-lg text-compass-87 font-bold">$5,000</div>
            </div>
          </div>

          <div class="w-full">
            <div class="flex items-center mb-2 justify-between">
              <div class="flex items-center">
                <mat-icon
                  class="mx-1.5 text-thermic mat-tm-icon mat-tm-icon-size-10"
                >
                  lens
                </mat-icon>
                <div class="text-sm text-compass-60">Overdue amount</div>
              </div>

              <div class="text-base text-compass-87 font-bold text-thermic">
                $2,000
              </div>
            </div>

            <div class="flex items-center mb-2 justify-between">
              <div class="flex items-center">
                <mat-icon
                  class="mx-1.5 text-pacificBridge mat-tm-icon mat-tm-icon-size-10"
                >
                  lens
                </mat-icon>
                <div class="text-sm text-compass-60">Principal</div>
              </div>
              <div class="text-base text-compass-87 font-bold">$2,000</div>
            </div>

            <div class="flex items-center mb-2 justify-between">
              <div class="flex items-center">
                <mat-icon
                  class="mx-1.5 text-thunder mat-tm-icon mat-tm-icon-size-10"
                >
                  lens
                </mat-icon>

                <div class="text-sm text-compass-60">Interest</div>
              </div>
              <div class="text-base text-compass-87 font-bold">$200</div>
            </div>

            <div class="flex items-center mb-2 justify-between">
              <div class="flex items-center">
                <mat-icon
                  class="mx-1.5 text-lime mat-tm-icon mat-tm-icon-size-10"
                >
                  lens
                </mat-icon>
                <div class="text-sm text-compass-60">Loan fees</div>
              </div>
              <div class="text-base text-compass-87 font-bold">$100</div>
            </div>

            <div class="flex items-center mb-2 justify-between">
              <div class="flex items-center">
                <mat-icon
                  class="mx-1.5 text-lobster mat-tm-icon mat-tm-icon-size-10"
                >
                  lens
                </mat-icon>
                <div class="text-sm text-compass-60">Portfolio fees</div>
              </div>
              <div class="text-base text-compass-87 font-bold">$100</div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid col-span-2 mx-4">
        <div class="font-semibold font">Documents</div>
      </div>
    </div>
  </div> -->

      <ng-container *ngIf="getOverview$ | async as overview">
        <div class="mb-6 main-card" *ngIf="overview.totalDebtAmount > 0">
          <div class="border-compass">
            <div class="grid grid-cols-12 items-center gap-4 mb-2">
              <div
                class="col-span-8 flex text-compass-87 font-semibold text-2xl"
              >
                <div>
                  {{
                    'credit-limit-details.earlyRepayment.overdue' | transloco
                  }}
                </div>
                <ng-container *ngIf="getSimulationOverdue$ | async as overdue">
                  <e2e-overdue-tooltip
                    class="leading-[0] self-center"
                    [totalOverdue]="overdue"
                    tooltipPosition="left"
                  ></e2e-overdue-tooltip>
                </ng-container>
              </div>
              <div class="col-span-2 text-right me-4">
                <div
                  *ngIf="
                    earlyRepaymentForm.repaymentType ===
                      EarlyRepaymentTypes.Full ||
                      (earlyRepaymentForm.repaymentType ===
                        EarlyRepaymentTypes.Partial &&
                        earlyRepaymentForm.amountToRepay &&
                        earlyRepaymentForm.amountToRepay >=
                          overview.totalDebtAmount);
                    else partialMode
                  "
                  class="operation-status early-repayment-status-full"
                >
                  <!-- [ngClass]="
                  'early-repayment-status-' + portfolio.mode | uppercase
                " -->
                  {{
                    'credit-limit-details.earlyRepayment.full'
                      | transloco
                      | uppercase
                  }}
                </div>

                <ng-template #partialMode>
                  <div class="operation-status early-repayment-status-partial">
                    {{
                      'credit-limit-details.earlyRepayment.partial'
                        | transloco
                        | uppercase
                    }}
                  </div>
                </ng-template>
              </div>
              <div
                class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
              >
                <div
                  *ngIf="
                    earlyRepaymentForm.amountToRepay &&
                      earlyRepaymentForm.amountToRepay > 0 &&
                      earlyRepaymentForm.amountToRepay <
                        overview.totalDebtAmount;
                    else partialModeTotalDebtAmount
                  "
                >
                  {{
                    earlyRepaymentForm.amountToRepay
                      | currency: currency[0].code
                  }}
                </div>
                <ng-template #partialModeTotalDebtAmount>
                  {{ overview.totalDebtAmount | currency: currency[0].code }}
                </ng-template>
              </div>
            </div>

            <div class="bg-maWhite rounded-lg px-6 py-4">
              <div class="grid grid-cols-12 items-center gap-4">
                <div class="col-span-3 text-compass font-semibold text-lg">
                  {{
                    'credit-limit-details.earlyRepayment.overdueRepayment'
                      | transloco
                  }}
                </div>
                <div class="col-span-5 text-compass-87 font-semibold text-xs">
                  <button
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="openDebtRepaymentTable(overview.totalDebtAmount)"
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                </div>

                <div class="col-span-2 text-right">
                  <div
                    *ngIf="
                      earlyRepaymentForm.repaymentType ===
                        EarlyRepaymentTypes.Full ||
                        (earlyRepaymentForm.repaymentType ===
                          EarlyRepaymentTypes.Partial &&
                          earlyRepaymentForm.amountToRepay &&
                          earlyRepaymentForm.amountToRepay >=
                            overview.totalDebtAmount);
                      else partialModeRepayment
                    "
                    class="operation-status early-repayment-status-full"
                  >
                    {{
                      'credit-limit-details.earlyRepayment.full'
                        | transloco
                        | uppercase
                    }}
                  </div>

                  <ng-template #partialModeRepayment>
                    <div
                      class="operation-status early-repayment-status-partial"
                    >
                      {{
                        'credit-limit-details.earlyRepayment.partial'
                          | transloco
                          | uppercase
                      }}
                    </div>
                  </ng-template>
                </div>
                <div
                  class="col-span-2 text-compass-87 font-semibold text-lg text-right"
                >
                  <div
                    *ngIf="
                      earlyRepaymentForm.amountToRepay &&
                        earlyRepaymentForm.amountToRepay > 0 &&
                        earlyRepaymentForm.amountToRepay <
                          overview.totalDebtAmount;
                      else partialModeRepaymentTotalDebtAmount
                    "
                  >
                    {{
                      earlyRepaymentForm.amountToRepay
                        | currency: currency[0].code
                    }}
                  </div>
                  <ng-template #partialModeRepaymentTotalDebtAmount>
                    {{ overview.totalDebtAmount | currency: currency[0].code }}
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          *ngIf="
            earlyRepaymentForm.repaymentType === EarlyRepaymentTypes.Full ||
            (earlyRepaymentForm.repaymentType === EarlyRepaymentTypes.Partial &&
              earlyRepaymentForm.amountToRepay &&
              earlyRepaymentForm.amountToRepay > overview.totalDebtAmount)
          "
          class="mb-6 main-card"
        >
          <!-- *ngFor="
            let creditLimit of limitDetails.creditLimits;
            index as creditLimitIndex;
            last as isLast;
            trackBy: trackByFn
          " -->

          <div
            *ngFor="let portfolio of selectedPortfolios"
            class="border-compass mb-4"
          >
            <div class="grid grid-cols-12 items-center mb-2 gap-4">
              <div class="col-span-8 text-compass-87 font-semibold text-2xl">
                {{
                  'credit-limit-details.earlyRepayment.portfolio' | transloco
                }}
                {{ portfolio.portfolio.referenceName }}
                <tm-tooltip
                  [escape]="false"
                  [tooltipTemplateMessage]="portfolioTooltipTemplate"
                >
                  <div #portfolioTooltipTemplate>
                    <div class="grid grid-cols-2 gap-2">
                      <ng-container *ngIf="portfolio.portfolio.debtAmount > 0">
                        <div class="text-sm font-light">
                          {{
                            'credit-limit-details.portfolioTooltip.overdueAmount'
                              | transloco
                          }}
                        </div>
                        <div class="flex">
                          <div
                            class="text-sm font-extrabold bg-once rounded-2xl px-3 py-1"
                          >
                            {{
                              portfolio.portfolio.debtAmount
                                | currency: currency[0].code
                            }}
                          </div>
                        </div>
                      </ng-container>

                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.portfolioAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          portfolio.portfolio.portfolioAmount
                            | currency: currency[0].code
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.utilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          portfolio.portfolio.utilizedAmount
                            | currency: currency[0].code
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.unutilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          portfolio.portfolio.unutilizedAmount
                            | currency: currency[0].code
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.purpose'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ portfolio.portfolio.purpose }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.effectiveDate'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          portfolio.portfolio.effectiveDate | date: 'dd.MM.yyyy'
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.endUtilizationDate'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          portfolio.portfolio.endUtilizationDate
                            | date: 'dd.MM.yyyy'
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'credit-limit-details.portfolioTooltip.maturityDate'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ portfolio.portfolio.maturityDate }}
                      </div>
                    </div>
                  </div>
                </tm-tooltip>
              </div>
              <div class="col-span-2 text-right me-4">
                <div
                  class="operation-status"
                  [ngClass]="
                    'early-repayment-status-' + portfolio.mode | lowercase
                  "
                >
                  {{
                    'credit-limit-details.earlyRepayment.' +
                      (portfolio.mode | lowercase)
                      | transloco
                      | uppercase
                  }}
                </div>
              </div>
              <div
                class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
              >
                <span
                  *ngIf="
                    portfolio.portfolio.earlyRepaymentType ===
                    EarlyRepaymentTypes.Partial
                  "
                >
                  {{
                    portfolio.portfolio.earlyRepaymentAmount
                      | currency: currency[0].code
                  }}
                </span>
                <span
                  *ngIf="
                    portfolio.portfolio.earlyRepaymentType ===
                    EarlyRepaymentTypes.Full
                  "
                >
                  {{
                    portfolio.portfolio.amountFullRepayment
                      | currency: currency[0].code
                  }}
                </span>
              </div>
            </div>

            <!-- <pre>{{ portfolio.portfolio | json }}</pre> -->
            <div
              class="bg-maWhite rounded-lg px-6 py-4 mb-4"
              *ngFor="let track of portfolio.portfolio.tracks; let i = index"
            >
              <div
                class="grid grid-cols-12 items-center gap-4 border-b mb-4 pb-4"
              >
                <div class="col-span-3 text-lead font-black text-sm">
                  <!-- {{ portfolio.portfolio.tracks[0].product }} -->
                  <!-- TRACK #{{ i + 1 }} -->
                  {{ 'credit-limit-details.moneyAllocation.track' | transloco }}
                  {{ track.referenceName }}
                  <tm-tooltip
                    [escape]="false"
                    [tooltipTemplateMessage]="portfolioTooltipTemplate"
                  >
                    <div #portfolioTooltipTemplate>
                      <div class="grid grid-cols-2 gap-2">
                        <div class="text-sm font-light">
                          {{
                            'credit-limit-details.trackTooltip.trackAmount'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ track.trackAmount | currency: currency[0].code }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'credit-limit-details.trackTooltip.utilizedAmount'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            track.utilizedAmount | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'credit-limit-details.trackTooltip.unutilizedAmount'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            track.unutilizedAmount | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'credit-limit-details.moneyAllocation.product'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ track.product }}
                        </div>
                      </div>
                    </div>
                  </tm-tooltip>
                </div>
                <div class="col-span-5 text-compass-87 font-semibold text-xs">
                  {{
                    'credit-limit-details.moneyAllocation.product'
                      | transloco
                      | uppercase
                  }}
                  <span class="text-lead">
                    {{ track.product }}
                  </span>
                </div>

                <div class="col-span-2 text-right">
                  <div
                    class="operation-status"
                    [ngClass]="
                      'early-repayment-status-' + track.earlyRepaymentType
                        | lowercase
                    "
                  >
                    {{
                      'credit-limit-details.earlyRepayment.' +
                        (track.earlyRepaymentType | lowercase)
                        | transloco
                        | uppercase
                    }}
                  </div>
                </div>
                <div
                  class="col-span-2 text-lead font-semibold text-lg text-right"
                >
                  <span
                    *ngIf="
                      track.earlyRepaymentType === EarlyRepaymentTypes.Partial
                    "
                  >
                    {{
                      track.earlyRepaymentAmount | currency: currency[0].code
                    }}
                  </span>
                  <span
                    *ngIf="
                      track.earlyRepaymentType === EarlyRepaymentTypes.Full
                    "
                  >
                    {{ track.amountFullRepayment | currency: currency[0].code }}
                  </span>
                </div>
              </div>

              <div
                *ngFor="let loan of track.loans; let loanIndex = index"
                class="grid grid-cols-12 mb-2 items-center gap-4"
              >
                <div
                  class="col-span-3 text-compass font-semibold text-lg flex items-center"
                >
                  <!-- #{{ loanIndex + 1 }} -->
                  <div>
                    {{
                      'credit-limit-details.moneyAllocation.loan' | transloco
                    }}
                    {{ loan.referenceName }}
                  </div>
                  <div>
                    <tm-tooltip
                      [escape]="false"
                      [tooltipTemplateMessage]="portfolioTooltipTemplate"
                    >
                      <div #portfolioTooltipTemplate>
                        <div class="grid grid-cols-2 gap-2">
                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.loanAmount'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.loanAmount | currency: currency[0].code }}
                          </div>
                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.outstandingPrincipal'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              loan.outstandingPrincipal
                                | currency: currency[0].code
                            }}
                          </div>
                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.amountFullRepayment'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              loan.amountFullRepayment
                                | currency: currency[0].code
                            }}
                          </div>
                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.disbursementDate'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.disbursementDate | date: 'dd.MM.yyyy' }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.maturityDate'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.maturityDate | date: 'dd.MM.yyyy' }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.status'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.status }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.numberOfPayments'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.numberOfPayments }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.interestType'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.interestType }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.regularInterestRate'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              loan.regularInterestRate * 100 | number: '1.2-2'
                            }}%
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'credit-limit-details.loanTooltip.financialSource'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{ loan.financialSource }}
                          </div>
                        </div>
                      </div>
                    </tm-tooltip>
                  </div>
                </div>
                <div class="col-span-5">
                  <button
                    *ngIf="loan.earlyRepaymentType === EarlyRepaymentTypes.Full"
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="openFullRepaymentTable(loan.id)"
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                  <button
                    *ngIf="
                      loan.earlyRepaymentType === EarlyRepaymentTypes.Partial
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="openPartialRepaymentTable(loan.id)"
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                </div>

                <div class="col-span-2 text-right">
                  <div
                    class="operation-status"
                    [ngClass]="
                      'early-repayment-status-' + loan.earlyRepaymentType
                        | lowercase
                    "
                  >
                    {{
                      'credit-limit-details.earlyRepayment.' +
                        (loan.earlyRepaymentType | lowercase)
                        | transloco
                        | uppercase
                    }}
                  </div>
                </div>
                <div
                  class="col-span-2 text-compass-87 font-semibold text-lg text-right"
                >
                  <!-- {{ loan.amountFullRepayment | currency: currency[0].code }} -->
                  <span
                    *ngIf="
                      loan.earlyRepaymentType === EarlyRepaymentTypes.Partial
                    "
                  >
                    {{ loan.earlyRepaymentAmount | currency: currency[0].code }}
                  </span>
                  <span
                    *ngIf="loan.earlyRepaymentType === EarlyRepaymentTypes.Full"
                  >
                    {{ loan.amountFullRepayment | currency: currency[0].code }}
                  </span>
                </div>
              </div>

              <!-- TODO Hide track fees -->
              <!-- <div
              *ngFor="let fee of portfolio.portfolio.fees"
              class="grid grid-cols-12 mb-2 items-center gap-4"
            >
              <div class="col-span-3 text-compass font-semibold text-lg">
                {{
                  'e2e.repaymentTable.paymentFeeTypes.' + fee.entryType
                    | transloco
                }}
              </div>
              <div class="col-span-5"></div>

              <div class="col-span-2 text-right">
                <div
                  class="operation-status"
                  [ngClass]="'early-repayment-status-full' | uppercase"
                >
                  FULL
                </div>
              </div>
              <div
                class="col-span-2 text-compass-87 font-semibold text-lg text-right"
              >
                {{ fee.chargeAmount | currency: currency[0].code }}
              </div>
            </div> -->
            </div>
          </div>
        </div>
        <!-- <pre>{{ selectedPortfolios | json }}</pre> -->
      </ng-container>
    </div>
  </ng-container>
</ng-container>
