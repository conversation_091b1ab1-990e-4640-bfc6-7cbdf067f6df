<ng-container
  *ngIf="
    getCreditLimitEarlyRepaymentDetails$
      | async as creditLimitEarlyRepaymentDetails
  "
>
  <ng-container *ngIf="getDateFormat$ | async as dateFormat">
    <ng-container *ngIf="getCurrency$ | async as currency">
      <mat-accordion>
        <mat-panel-title class="flex justify-between items-center mr-0 mb-6">
          <div class="text-compass-87 font-medium text-2xl">
            {{
              'application-details.credit-limit-early-repayment.name'
                | transloco
            }}
          </div>
        </mat-panel-title>

        <p class="text-[15px] font-extrabold text-compass mb-2.5 uppercase">
          {{
            'application-details.credit-limit-early-repayment.creditLimitDetails'
              | transloco
          }}
        </p>

        <div class="grid grid-cols-2 3xl2:grid-cols-3 gap-4">
          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.limitAmount'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.limitAmount
                  | currency: currency[0].code
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.referenceName'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{ creditLimitEarlyRepaymentDetails?.creditLimit?.referenceName }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.status'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.status
                  | transloco
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.effectiveDate'
                  | transloco
              }}
            </div>
            <div class="flex flex-wrap">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.effectiveDate
                  | date: dateFormat
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.maturityDate'
                  | transloco
              }}
            </div>
            <div class="flex flex-wrap">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.endDate
                  | date: dateFormat
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.utilizedAmount'
                  | transloco
              }}
            </div>
            <div class="flex flex-wrap">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.utilizedAmount
                  | currency: currency[0].code
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.unutilizedAmount'
                  | transloco
              }}
            </div>
            <div class="flex flex-wrap">
              {{
                creditLimitEarlyRepaymentDetails?.creditLimit?.unutilizedAmount
                  | currency: currency[0].code
              }}
            </div>
          </div>

          <div></div>

          <div>
            <button
              type="button"
              mat-stroked-button
              color="primary"
              type="button"
              class="rounded-lg h-[38px] min-w-[140px]"
              (click)="
                openCreditLimit(
                  creditLimitEarlyRepaymentDetails?.creditLimit?.id
                )
              "
            >
              <mat-icon svgIcon="icon-view"></mat-icon>
              <span class="text-sm">
                {{ 'general.buttons.moreDetails' | transloco }}
              </span>
            </button>
          </div>
        </div>

        <mat-divider class="!my-6 border-compass-22"></mat-divider>

        <p class="text-[15px] font-extrabold text-compass mb-2.5 uppercase">
          <span class="text-sheffieldGrey">
            {{ 'e2e.earlyRepayment.requestBy' | transloco }}
          </span>
          <span>
            <ng-container
              *ngIf="creditLimitEarlyRepaymentDetails?.identity?.person"
            >
              {{ creditLimitEarlyRepaymentDetails.identity?.person?.name }}
              {{
                creditLimitEarlyRepaymentDetails.identity?.person?.familyName
              }}
            </ng-container>

            <ng-container
              *ngIf="creditLimitEarlyRepaymentDetails?.identity?.company"
            >
              {{ creditLimitEarlyRepaymentDetails.identity.company.name }}
            </ng-container>
          </span>
        </p>
        <div class="grid grid-cols-2 3xl2:grid-cols-3 gap-4">
          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.repaymentType'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                'application-details.credit-limit-early-repayment.' +
                  creditLimitEarlyRepaymentDetails.repaymentType?.toLowerCase()
                  | transloco
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.requestDate'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                creditLimitEarlyRepaymentDetails.requestedBy?.requestDate
                  | date: dateFormat
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.effectiveDate'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                creditLimitEarlyRepaymentDetails.effectiveDate
                  | date: dateFormat
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.repaymentAmount'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                creditLimitEarlyRepaymentDetails.amount?.value
                  | currency: currency[0].code
              }}
            </div>
          </div>
          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.repaymentDistribution'
                  | transloco
              }}
            </div>
            <div
              class="text-compass font-semibold"
              *ngIf="creditLimitEarlyRepaymentDetails.repaymentDistribution"
            >
              {{
                'e2e.spreadMethods.' +
                  creditLimitEarlyRepaymentDetails.repaymentDistribution
                  | transloco
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.paymentMethod'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              {{
                'e2e.paymentMethods.' +
                  creditLimitEarlyRepaymentDetails.payers?.paymentMethod
                  | transloco
              }}
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.payerName'
                  | transloco
              }}
            </div>
            <div class="text-compass font-semibold">
              <ng-container
                *ngIf="creditLimitEarlyRepaymentDetails?.payer?.person"
              >
                {{ creditLimitEarlyRepaymentDetails.payer?.person?.name }}
                {{ creditLimitEarlyRepaymentDetails.payer?.person?.familyName }}
              </ng-container>

              <ng-container
                *ngIf="creditLimitEarlyRepaymentDetails?.payer?.company"
              >
                {{ creditLimitEarlyRepaymentDetails.payer?.company?.name }}
              </ng-container>
            </div>
          </div>

          <div>
            <div class="text-compass-60">
              {{
                'application-details.credit-limit-early-repayment.account'
                  | transloco
              }}
            </div>
            <div
              class="text-compass font-semibold"
              *ngIf="
                creditLimitEarlyRepaymentDetails.payers?.stakeholderBankDetails
                  ?.bankNumber &&
                creditLimitEarlyRepaymentDetails.payers?.stakeholderBankDetails
                  ?.branchNumber &&
                creditLimitEarlyRepaymentDetails.payers?.stakeholderBankDetails
                  ?.accountNumber
              "
            >
              {{
                creditLimitEarlyRepaymentDetails.payers?.stakeholderBankDetails
                  ?.bankNumber +
                  '-' +
                  creditLimitEarlyRepaymentDetails.payers
                    ?.stakeholderBankDetails?.branchNumber +
                  '-' +
                  creditLimitEarlyRepaymentDetails.payers
                    ?.stakeholderBankDetails?.accountNumber
              }}
            </div>
          </div>

          <!-- <div>
              <div class="text-compass-60">
                {{ 'application-details.credit-limit-early-repayment.moneyAllocation' | transloco  }}
              </div>
              <div class="text-compass font-semibold">
                 {{ creaditLimitEarlyrepaymentDetails.moneyAllocation }}
              </div>
            </div> -->
        </div>
        <mat-divider class="!my-6 border-compass-22"></mat-divider>

        <p class="text-[15px] font-extrabold text-compass mb-2.5 uppercase">
          {{
            'application-details.credit-limit-early-repayment.fees' | transloco
          }}
        </p>

        <div class="grid grid-cols-2 3xl2:grid-cols-3 gap-4">
          <ng-container
            *ngFor="let fee of creditLimitEarlyRepaymentDetails.fees"
          >
            <div *ngIf="fee.isEnabled">
              <e2e-fee [fee]="fee"></e2e-fee>
            </div>
          </ng-container>
        </div>
      </mat-accordion>
    </ng-container>
  </ng-container>
</ng-container>
