import { E2eQueryParams, requestParamsToStateParams } from '@e2e/lib/types';
import { createReducer, on } from '@ngrx/store';
import { TenantManagementLibPageActions } from '@tenant-management/lib/state';
import {
  HttpState,
  TableColumns,
  updateStateData,
} from '@tenant-management/lib/types';
import { LoansResponse } from '../models/loans-response';
import { defaultLoansTableColumns } from '../utils/loans-table-columns';
import { LoansApiActions, LoansPageActions } from './actions';

export interface LoansState {
  loans: HttpState<LoansResponse>;
  isFiltersPanelShown: boolean;
  queryParams: E2eQueryParams;
  loansTableColumns: TableColumns[];
}

export const initialState: LoansState = {
  loans: {} as HttpState<LoansResponse>,
  isFiltersPanelShown: false,
  queryParams: {
    active: 'loanId',
    direction: 'desc',
    page: 0,
    size: 15,
    searchType: 'LOAN_ID',
    search: '',
  } as E2eQueryParams,
  loansTableColumns: defaultLoansTableColumns,
};

export const loansReducer = createReducer(
  initialState,
  on(LoansApiActions.getLoansSuccess, (state, { loans }): LoansState => {
    return {
      ...state,
      loans: updateStateData(state.loans, loans),
      isFiltersPanelShown: loans.loading ? state.isFiltersPanelShown : false,
    };
  }),
  on(LoansApiActions.getLoansFailure, (state, error): LoansState => {
    return {
      ...state,
      loans: {
        loading: false,
        error,
      },
    };
  }),
  on(LoansPageActions.getInitialLoans, (state, { queryParams }): LoansState => {
    const updatedQueryParams = requestParamsToStateParams(queryParams);

    return {
      ...state,
      queryParams: { ...state.queryParams, ...updatedQueryParams },
    };
  }),
  on(LoansPageActions.setQueryParams, (state, { queryParams }): LoansState => {
    const incomingSearchTerm = queryParams.search || '';
    let updatedQueryParams = queryParams;

    if (state.queryParams.search !== incomingSearchTerm) {
      updatedQueryParams = {
        ...queryParams,
        page: 0,
      };
    }
    return {
      ...state,
      queryParams: { ...state.queryParams, ...updatedQueryParams },
    };
  }),
  on(LoansPageActions.setFilters, (state, { filters }): LoansState => {
    return {
      ...state,
      queryParams: {
        ...state.queryParams,
        ...filters,
        page: 0,
        size: state.queryParams.size,
      },
    };
  }),
  on(LoansPageActions.resetFilters, (state): LoansState => {
    return {
      ...state,
      queryParams: {
        ...state.queryParams,
        period: null,
        periodType: null,
        statuses: null,
        subStatuses: null,
        startDate: null,
        endDate: null,
        type: null,
      },
    };
  }),
  on(
    LoansPageActions.toggleFiltersPanel,
    (state, { isFiltersPanelShown }): LoansState => {
      return {
        ...state,
        isFiltersPanelShown,
      };
    }
  ),
  on(
    LoansPageActions.updateLoansTableColumns,
    (state, { updatedColumns }): LoansState => {
      return {
        ...state,
        loansTableColumns: updatedColumns,
      };
    }
  ),
  on(TenantManagementLibPageActions.clearState, (state): LoansState => {
    return {
      ...state,
      loansTableColumns: defaultLoansTableColumns,
    };
  })
);
